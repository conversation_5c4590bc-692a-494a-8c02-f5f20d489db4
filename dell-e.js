import OpenAI from "openai";


const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************", // or directly: apiKey: "sk-...",
});

async function generateImage() {
  const response = await openai.images.generate({
    model: "dall-e-3", // or "dall-e-2"
    prompt: "A futuristic city floating in the sky with waterfalls",
    n: 1,
    size: "1024x1024", // other options: "256x256", "512x512"
    response_format: "url", // or "b64_json"
  });

  console.log(response.data[0].url); // Use this URL in frontend or download it
}

generateImage();
