
import React, { useState, useEffect } from "react";
import { Question } from "./types";

interface ActiveQuestionProps {
    currentQuestion: Question;
    onSubmitAnswer: (answer: string) => void;
    onNextQuestion: () => void;
}

const ActiveQuestion: React.FC<ActiveQuestionProps> = ({
    currentQuestion,
    onSubmitAnswer,
    onNextQuestion,
}) => {
    const [selectedOption, setSelectedOption] = useState<string | null>(null);
    const [isAnswered, setIsAnswered] = useState(false);

    useEffect(() => {
        // Reset state when the question changes
        setSelectedOption(null);
        setIsAnswered(false);
    }, [currentQuestion]);

    const handleOptionClick = (option: string) => {
        if (isAnswered) return; // Don't allow changes after answering

        setSelectedOption(option);
        setIsAnswered(true);
        onSubmitAnswer(option);

        // Automatically move to the next question after a short delay
        setTimeout(() => {
            onNextQuestion();
        }, 2000); // 2-second delay
    };

    const getOptionClass = (option: string) => {
        if (!isAnswered) {
            return 'border-gray-300 hover:border-spark-blue/50';
        }

        const isCorrect = option === currentQuestion.correct_answer;
        const isSelected = option === selectedOption;

        if (isCorrect) {
            return 'border-green-500 bg-green-100 text-green-800';
        }
        if (isSelected && !isCorrect) {
            return 'border-red-500 bg-red-100 text-red-800';
        }
        return 'border-gray-300';
    };

    return (
        <div className="space-y-6">
            <div className="bg-gradient-to-r from-spark-blue/10 to-spark-purple/10 p-6 rounded-lg">
                <div className="flex items-start gap-3 mb-4">
                    <div className="h-6 w-6 bg-spark-blue text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">
                        ?
                    </div>
                    <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {currentQuestion.question}
                        </h3>
                        <div className="flex gap-2 text-xs">
                            <span className="px-2 py-1 bg-spark-blue/20 text-spark-blue rounded-full">
                                {currentQuestion.difficulty}
                            </span>
                            <span className="px-2 py-1 bg-spark-purple/20 text-spark-purple rounded-full">
                                {currentQuestion.category}
                            </span>
                            {currentQuestion.is_ai_generated && (
                                <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                    🤖 AI Generated
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                <div className="mt-4">
                    {currentQuestion.question_type === 'mcq' && currentQuestion.options ? (
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                                Choose your answer:
                            </label>
                            <div className="space-y-2">
                                {currentQuestion.options.map((option, index) => (
                                    <label
                                        key={index}
                                        className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${getOptionClass(option)}`}
                                        onClick={() => handleOptionClick(option)}
                                    >
                                        <input
                                            type="radio"
                                            name="mcq-option"
                                            value={option}
                                            checked={selectedOption === option}
                                            readOnly
                                            className="mr-3"
                                            disabled={isAnswered}
                                        />
                                        <span className="text-gray-900">{option}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                    ) : (
                        // Keep open-ended question UI as is, but it won't be used for now
                        <div>
                            <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-2">
                                Your Answer:
                            </label>
                            <textarea
                                id="answer"
                                placeholder="Share your thoughts here..."
                                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-spark-blue focus:border-transparent resize-none"
                                rows={4}
                            />
                        </div>
                    )}
                </div>

                {isAnswered && (
                    <div className="mt-4 text-sm">
                        {selectedOption !== currentQuestion.correct_answer && (
                            <div className="text-red-600 mb-2">
                                <strong>Correct answer:</strong> {currentQuestion.correct_answer}
                            </div>
                        )}
                        <div className="text-gray-700">
                            <strong>Explanation:</strong> {currentQuestion.explanation}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ActiveQuestion;
