import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.9,
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    console.log('💡 [ART IDEA] ===== NEW IDEA REQUEST =====');
    const requestBody = await request.json().catch(() => ({ style: 'realistic' }));
    const { style = 'realistic' } = requestBody;
    console.log('💡 [ART IDEA] Request body:', requestBody);
    console.log('💡 [ART IDEA] Extracted style:', style);

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const styleCheck = await moderateContent(style || '', 'prompt', userKey);
    if (!styleCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: styleCheck.reason, blocked: styleCheck.blocked ?? false }, { status: 400 });
    }

    console.log('=== ART GENERATE IDEA API ===');
    console.log('Style:', style);
    console.log('GOOGLE_API_KEY available:', !!process.env.GOOGLE_API_KEY);

    if (!process.env.GOOGLE_API_KEY) {
      console.error('GOOGLE_API_KEY not configured');
      return NextResponse.json(
        { success: false, error: 'Google API key not configured' },
        { status: 500 }
      );
    }

    // Initialize image search for style-aware idea generation
    const imageSearch = new GoogleImageSearchTool();
    let styleContext = '';

    // Get style context from image search if available
    if (imageSearch.isAvailable()) {
      try {
        console.log(`[ArtIdea] Using image search to understand ${style} style context`);
        const searchResults = await imageSearch.searchImages(`${style} art examples`, style, 3);
        if (searchResults.length > 0) {
          styleContext = searchResults
            .map(result => result.title + ' ' + (result.snippet || ''))
            .join(' ')
            .toLowerCase();
          console.log(`[ArtIdea] Style context from search: ${styleContext.substring(0, 200)}...`);
        }
      } catch (error) {
        console.error('[ArtIdea] Image search for style context failed:', error);
      }
    }

    // Style-specific creative prompts
    const styleSpecificPrompts = {
      realistic: [
        "A majestic golden eagle soaring over snow-capped mountains during a breathtaking sunset",
        "A peaceful meadow filled with wildflowers where a gentle deer drinks from a crystal stream",
        "A cozy lighthouse standing tall against ocean waves under a starry night sky",
        "A beautiful butterfly garden with monarch butterflies dancing among colorful blooming flowers",
        "A wise old oak tree with a treehouse where woodland creatures gather for tea",
        "A serene lake reflecting autumn trees with their leaves in brilliant reds and golds",
        "A magnificent waterfall cascading into a rainbow-misted pool surrounded by lush greenery",
        "A friendly dolphin family playing in turquoise ocean waters near a tropical island"
      ],
      cartoon: [
        "A cheerful robot chef making rainbow cupcakes in a magical floating kitchen",
        "A brave little mouse knight riding a friendly dragon through fluffy cloud castles",
        "A group of animal friends having a picnic party in an enchanted forest clearing",
        "A smiling sun and moon playing hide-and-seek behind colorful mountain peaks",
        "A magical ice cream truck that flies through the sky delivering treats to clouds",
        "A family of singing vegetables dancing in a garden under twinkling fairy lights",
        "A curious kitten explorer discovering a treasure chest full of glowing gems",
        "A friendly alien family visiting Earth to learn about flowers and butterflies"
      ],
      comic: [
        "A superhero cat saving the day by stopping runaway balloons in a bustling city",
        "A brave young wizard casting colorful spells to protect a magical library",
        "A team of animal superheroes working together to clean up a polluted river",
        "A fearless space explorer discovering a planet made entirely of candy and sweets",
        "A heroic firefighter dog rescuing kittens from a tree during a sunny day",
        "A mighty superhero teacher helping students solve problems with the power of knowledge",
        "A courageous young inventor building amazing machines to help their community",
        "A group of superhero kids using their powers to plant trees and save forests"
      ],
      watercolor: [
        "A peaceful garden with blooming cherry blossoms and gentle butterflies floating softly",
        "A dreamy landscape of rolling hills covered in lavender fields under soft clouds",
        "A serene pond with lily pads where a graceful swan glides through morning mist",
        "A cozy cottage surrounded by wildflower meadows and a babbling brook",
        "A magical forest clearing where fairy lights dance among ancient trees",
        "A gentle rain shower creating puddles that reflect the colors of a rainbow",
        "A quiet beach at dawn with seashells scattered on soft sand and gentle waves",
        "A mountain valley filled with soft morning fog and blooming alpine flowers"
      ],
      pixel: [
        "A brave knight exploring a mysterious crystal cave filled with glowing treasure chests",
        "A retro spaceship adventure through a galaxy of colorful planets and friendly aliens",
        "A magical 8-bit forest where pixelated animals help a young hero find ancient gems",
        "A classic arcade-style underwater world with friendly sea creatures and coral castles",
        "A pixel art village where tiny characters celebrate a harvest festival with fireworks",
        "A nostalgic train journey through blocky mountains and pixelated countryside landscapes",
        "A retro gaming castle where a heroic character collects power-ups and magical items",
        "A charming 16-bit garden where pixel flowers bloom and digital butterflies flutter around"
      ]
    };

    const generateIdeaPrompt = `You are a creative art idea generator for children's artwork. Generate a completely original, imaginative, and child-friendly art prompt in ${style} style that would inspire kids to create beautiful artwork. Keep it within 25 words.

STYLE-SPECIFIC REQUIREMENTS FOR ${style.toUpperCase()}:
${style === 'realistic' ? `
- Focus on natural subjects like animals, landscapes, or nature scenes
- Emphasize beautiful lighting, textures, and realistic details
- Include peaceful, majestic, or serene themes
- Mention specific times of day or weather conditions for atmosphere
` : ''}${style === 'cartoon' ? `
- Create fun, whimsical characters with personality
- Include magical or fantastical elements
- Use bright, cheerful themes with friendly interactions
- Add playful scenarios that spark imagination
` : ''}${style === 'comic' ? `
- Design action-oriented scenes with heroic characters
- Include adventure, problem-solving, or helping others
- Create dynamic situations with clear protagonists
- Focus on positive superhero or adventure themes
` : ''}${style === 'watercolor' ? `
- Emphasize soft, gentle, and peaceful subjects
- Include natural elements like flowers, water, or landscapes
- Focus on serene, dreamy, or contemplative scenes
- Mention soft lighting or atmospheric effects
` : ''}${style === 'pixel' ? `
- Create retro gaming-inspired adventures
- Include exploration, collection, or quest elements
- Design blocky, nostalgic environments
- Focus on classic video game themes and characters
` : ''}

INSPIRATION EXAMPLES FOR ${style.toUpperCase()}:
${styleSpecificPrompts[style as keyof typeof styleSpecificPrompts]?.slice(0, 3).map((example, i) => `${i + 1}. ${example}`).join('\n') || ''}

IMPORTANT GUIDELINES:
1. Create completely original and unique ideas (don't copy the examples)
2. Keep content strictly child-friendly and positive
3. Use vivid, colorful, and imaginative descriptions
4. Focus on themes of wonder, magic, adventure, nature, friendship, and discovery
5. Avoid any scary, violent, or inappropriate content
6. Make it engaging and fun for children
7. Include interesting visual elements like colors, textures, or magical elements
8. Keep it concise but descriptive (1-2 sentences, max 25 words)

${styleContext ? `STYLE CONTEXT: Based on research, ${style} style often features: ${styleContext.substring(0, 300)}

` : ''}Generate a single, original art idea (max 25 words) that would inspire a child to create amazing ${style} artwork:`;

    console.log('Calling Gemini API for idea generation...');

    let generatedIdea: string | undefined;

    try {
      const response = await llm.invoke([
        {
          role: "user",
          content: generateIdeaPrompt
        }
      ]);

      generatedIdea = typeof response.content === 'string' ? response.content.trim() : response.content?.toString();
      console.log('Gemini API response:', generatedIdea);

      if (!generatedIdea) {
        throw new Error("Gemini API failed or returned empty result");
      }
    } catch (apiError) {
      console.error('Gemini API error for art idea:', apiError);

      // Provide fallback art ideas based on style
      const fallbackIdeas = {
        cartoon: styleSpecificPrompts.cartoon[Math.floor(Math.random() * styleSpecificPrompts.cartoon.length)],
        watercolor: styleSpecificPrompts.watercolor[Math.floor(Math.random() * styleSpecificPrompts.watercolor.length)],
        realistic: styleSpecificPrompts.realistic[Math.floor(Math.random() * styleSpecificPrompts.realistic.length)],
        comic: styleSpecificPrompts.comic[Math.floor(Math.random() * styleSpecificPrompts.comic.length)],
        pixel: styleSpecificPrompts.pixel[Math.floor(Math.random() * styleSpecificPrompts.pixel.length)]
      };

      generatedIdea = fallbackIdeas[style as keyof typeof fallbackIdeas] ||
                     "A magical creative artwork that sparks imagination and brings joy to everyone who sees it";

      console.log('Using fallback art idea due to API error:', generatedIdea);
    }

    console.log('Generated art idea:', generatedIdea);

    return NextResponse.json({
      success: true,
      idea: generatedIdea
    });

  } catch (error) {
    console.error('Error generating art idea:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate art idea' },
      { status: 500 }
    );
  }
} 

