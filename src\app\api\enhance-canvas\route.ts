import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

export async function POST(req: NextRequest) {
  const { imageData, prompt } = await req.json();
  console.log('🔥 ENHANCE API: Received request, prompt=', prompt);
  
  if (!imageData) {
    return NextResponse.json({ error: 'Image data is required' }, { status: 400 });
  }

  if (!process.env.GOOGLE_API_KEY) {
    return NextResponse.json({ error: 'Google API key not configured' }, { status: 500 });
  }

  const subject = prompt ? prompt : 'the artwork';

  // Initialize image search for enhanced canvas enhancement
  const imageSearch = new GoogleImageSearchTool();
  let enhancedPrompt = `photorealistic full color rendering of this ${subject}`;

  // Use image search to enhance the enhancement prompt
  if (imageSearch.isAvailable() && prompt) {
    try {
      console.log('🔥 ENHANCE API: Using image search to enhance canvas enhancement');
      const searchResults = await imageSearch.searchImages(`${prompt} realistic photo`, 'realistic', 3);
      if (searchResults.length > 0) {
        const searchContext = searchResults
          .map(result => result.title + ' ' + (result.snippet || ''))
          .join(' ')
          .toLowerCase();

        // Extract quality and realistic terms
        const qualityWords = searchContext.match(/\b(professional|high|quality|detailed|natural|realistic|beautiful|stunning|vibrant)\b/g) || [];
        if (qualityWords.length > 0) {
          const uniqueWords = [...new Set(qualityWords)].slice(0, 4);
          enhancedPrompt = `${uniqueWords.join(' ')} photorealistic full color rendering of this ${subject}`;
        }
      }
    } catch (error) {
      console.error('🔥 ENHANCE API: Image search enhancement failed:', error);
    }
  }

  // Create enhanced prompt for Gemini
  const enhancementPrompt = `${enhancedPrompt}, maintain composition and structure, add realistic textures, natural lighting, vibrant and balanced colors, detailed finish, professional photography quality, DSLR camera, sharp focus, high resolution`;

  console.log('🔥 ENHANCE API: Enhanced prompt:', enhancementPrompt);

  // Initialize Google GenAI
  const ai = new GoogleGenAI({
    apiKey: process.env.GOOGLE_API_KEY!,
  });

  const config = {
    responseModalities: ['IMAGE', 'TEXT'],
  };

  const model = 'gemini-2.0-flash-preview-image-generation';

  try {
    // Extract base64 data from data URL if needed
    let base64Data = imageData;
    let mimeType = 'image/png';

    if (imageData.startsWith('data:')) {
      const [header, data] = imageData.split(',');
      base64Data = data;
      const mimeMatch = header.match(/data:([^;]+)/);
      if (mimeMatch) {
        mimeType = mimeMatch[1];
      }
    }

    const contents = [
      {
        role: 'user' as const,
        parts: [
          {
            inlineData: {
              mimeType: mimeType,
              data: base64Data
            }
          },
          {
            text: enhancementPrompt,
          },
        ],
      },
    ];

    // Generate enhanced image using Gemini
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let enhancedImageData: string | null = null;
    let enhancedMimeType: string | null = null;

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        enhancedImageData = inlineData.data || null;
        enhancedMimeType = inlineData.mimeType || null;
        break; // We only need the first image
      }
    }

    if (!enhancedImageData || !enhancedMimeType) {
      throw new Error('Canvas enhancement failed - no image data received');
    }

    // Convert base64 to data URL for direct use
    const enhancedImageUrl = `data:${enhancedMimeType};base64,${enhancedImageData}`;

    console.log('🔥 ENHANCE API: Successfully enhanced canvas');
    return NextResponse.json({ image: enhancedImageUrl });

  } catch (err) {
    console.error('🔥 ENHANCE API: Gemini API error:', err);
    return NextResponse.json({
      error: 'Failed to enhance image',
      details: err instanceof Error ? err.message : 'Unknown error'
    }, { status: 500 });
  }
} 