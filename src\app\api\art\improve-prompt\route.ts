import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.7, 
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    const { prompt, style = 'cartoon' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }
    
    console.log('=== ART IMPROVE PROMPT API ===');
    console.log('GOOGLE_API_KEY available:', process.env.GOOGLE_API_KEY);
    
    if (!prompt) {
      console.error('No prompt provided');
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Initialize image search for enhanced prompt improvement
    const imageSearch = new GoogleImageSearchTool();
    let enhancedPromptFromSearch = prompt;

    // Use image search to enhance the prompt if available
    if (imageSearch.isAvailable()) {
      try {
        console.log(`[PromptImprove] Using image search to enhance prompt for ${style} style`);
        enhancedPromptFromSearch = await imageSearch.getEnhancedPromptFromSearch(prompt, style);
        console.log(`[PromptImprove] Search-enhanced prompt: ${enhancedPromptFromSearch}`);
      } catch (error) {
        console.error('[PromptImprove] Image search enhancement failed:', error);
      }
    }

    const improvePrompt = `You are an expert art prompt enhancer for children's artwork. Take the following basic prompt and improve it to create a more vivid, child-friendly ${style} style description. Keep the final result within 25 words.

STYLE-SPECIFIC ENHANCEMENT GUIDELINES FOR ${style.toUpperCase()}:
${style === 'realistic' ? `
- Add natural lighting descriptions (golden hour, soft sunlight, gentle shadows)
- Include realistic textures and materials (smooth fur, rough bark, crystal clear water)
- Mention atmospheric elements (morning mist, gentle breeze, sparkling dewdrops)
- Focus on natural beauty and peaceful scenes
- Add specific details about colors found in nature
` : ''}${style === 'cartoon' ? `
- Add personality traits and expressions (cheerful, curious, friendly, playful)
- Include bright, vibrant colors (rainbow, neon, pastel, glowing)
- Add magical or whimsical elements (sparkles, floating objects, talking animals)
- Create fun interactions between characters
- Use playful descriptive words (bouncy, giggly, wiggly, fluffy)
` : ''}${style === 'comic' ? `
- Add action words and dynamic movement (soaring, leaping, dashing, zooming)
- Include heroic qualities (brave, mighty, courageous, determined)
- Add dramatic lighting effects (bold shadows, bright highlights, glowing auras)
- Create sense of adventure and excitement
- Include environmental details that suggest action (wind-blown, dramatic poses)
` : ''}${style === 'watercolor' ? `
- Add soft, flowing descriptive words (gentle, dreamy, ethereal, serene)
- Include water-related elements (flowing, misty, dewy, rippling)
- Mention soft color transitions (blending, fading, washing, blooming)
- Focus on peaceful, contemplative scenes
- Add atmospheric effects (soft light, gentle shadows, morning haze)
` : ''}${style === 'pixel' ? `
- Add retro gaming elements (power-ups, collectibles, platforms, levels)
- Include blocky, geometric descriptions (chunky, square, pixelated, 8-bit)
- Mention classic gaming colors (bright primary colors, neon effects)
- Add adventure or exploration elements (quest, treasure, discovery)
- Use nostalgic gaming terminology (sprite, character, world, stage)
` : ''}

ENHANCEMENT EXAMPLES FOR ${style.toUpperCase()}:
${style === 'realistic' ? `
- "cat" → "a majestic orange tabby cat with emerald eyes basking in golden sunlight"
- "tree" → "an ancient oak tree with textured bark and leaves dancing in gentle breeze"
- "flower" → "a delicate rose with velvety petals glistening with morning dewdrops"
` : ''}${style === 'cartoon' ? `
- "cat" → "a cheerful cartoon cat with big sparkling eyes wearing a rainbow bowtie"
- "tree" → "a magical talking tree with a friendly face and colorful fruit"
- "flower" → "a giggling sunflower with rosy cheeks swaying in the breeze"
` : ''}${style === 'comic' ? `
- "cat" → "a heroic super-cat with a flowing cape leaping through the city skyline"
- "tree" → "a mighty guardian tree with glowing branches protecting the forest"
- "flower" → "a powerful flower warrior with petals that shine like armor"
` : ''}${style === 'watercolor' ? `
- "cat" → "a graceful cat with soft fur painted in gentle watercolor washes"
- "tree" → "a dreamy willow tree with branches flowing like watercolor brushstrokes"
- "flower" → "delicate wildflowers blooming in soft, blended pastel colors"
` : ''}${style === 'pixel' ? `
- "cat" → "a pixelated adventure cat collecting gems in a retro 8-bit world"
- "tree" → "a blocky pixel tree in a classic gaming forest level"
- "flower" → "a glowing pixel flower power-up in a nostalgic game garden"
` : ''}

IMPORTANT GUIDELINES:
1. Keep content strictly child-friendly and positive
2. Add vivid colors, textures, and visual details specific to ${style} style
3. Make descriptions engaging and imaginative
4. Focus on themes of wonder, magic, and adventure
5. Avoid any scary, violent, or inappropriate content
6. Use clear, descriptive language appropriate for ${style} art
7. Keep the core concept but make it more detailed and artistic
8. Limit the result to 25 words maximum

Original prompt: "${prompt}"
${enhancedPromptFromSearch !== prompt ? `Search-enhanced version: "${enhancedPromptFromSearch}"` : ''}

Please provide an improved, more detailed ${style} style version of this prompt that would create better artwork:`;

    console.log('Calling Gemini API for prompt improvement...');

    let initialImprovedPrompt: string;

    try {
      const response = await llm.invoke([
        {
          role: "user",
          content: improvePrompt
        }
      ]);

      initialImprovedPrompt = String(response.content);
    } catch (apiError) {
      console.error('Gemini API error for prompt improvement:', apiError);

      // Provide fallback improved prompts based on style
      const fallbackImprovements = {
        cartoon: `A cheerful cartoon ${prompt} with sparkling eyes, rainbow colors, and magical whimsical details that bring joy`,
        watercolor: `A dreamy watercolor ${prompt} with soft flowing brushstrokes, gentle pastel colors, and ethereal misty atmosphere`,
        realistic: `A majestic realistic ${prompt} with natural golden lighting, detailed textures, and breathtaking lifelike beauty`,
        comic: `A heroic comic book ${prompt} with bold dynamic action, dramatic lighting effects, and superhero adventure energy`,
        pixel: `A nostalgic 8-bit ${prompt} with chunky pixel art style, bright retro colors, and classic gaming adventure vibes`
      };

      initialImprovedPrompt = fallbackImprovements[style as keyof typeof fallbackImprovements] ||
                             `An enhanced artistic version of ${prompt} with beautiful details, vibrant colors, and creative visual elements`;

      console.log('Using fallback improved prompt due to API error:', initialImprovedPrompt);
    }

    const outCheck = await moderateContent(initialImprovedPrompt, 'text');
    const improvedPrompt = outCheck.isAppropriate ? initialImprovedPrompt : "Let's think of a different artistic idea!";
    console.log('Improved prompt:', improvedPrompt);

    return NextResponse.json({
      success: true,
      improvedPrompt,
      originalPrompt: prompt
    });

  } catch (error) {
    console.error('Error improving prompt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to improve prompt' },
      { status: 500 }
    );
  }
} 



