import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.7, 
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    const { prompt, style = 'cartoon' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }
    
    console.log('=== ART IMPROVE PROMPT API ===');
    console.log('GOOGLE_API_KEY available:', process.env.GOOGLE_API_KEY);
    
    if (!prompt) {
      console.error('No prompt provided');
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Initialize image search for enhanced prompt improvement
    const imageSearch = new GoogleImageSearchTool();
    let enhancedPromptFromSearch = prompt;

    // Use image search to enhance the prompt if available
    if (imageSearch.isAvailable()) {
      try {
        console.log(`[PromptImprove] Using image search to enhance prompt for ${style} style`);
        enhancedPromptFromSearch = await imageSearch.getEnhancedPromptFromSearch(prompt, style);
        console.log(`[PromptImprove] Search-enhanced prompt: ${enhancedPromptFromSearch}`);
      } catch (error) {
        console.error('[PromptImprove] Image search enhancement failed:', error);
      }
    }

    const improvePrompt = `You are an expert art prompt enhancer for children's artwork. Take the following basic prompt and improve it to create a more vivid, child-friendly ${style} style description. Keep the final result within 25 words.

IMPORTANT GUIDELINES:
1. Keep content strictly child-friendly and positive
2. Add vivid colors, textures, and visual details
3. Make descriptions engaging and imaginative
4. Focus on themes of wonder, magic, and adventure
5. Avoid any scary, violent, or inappropriate content
6. Use clear, descriptive language
7. Keep the core concept but make it more detailed and artistic
8. Limit the result to 25 words maximum

Original prompt: "${prompt}"
${enhancedPromptFromSearch !== prompt ? `Search-enhanced version: "${enhancedPromptFromSearch}"` : ''}

Please provide an improved, more detailed version of this prompt that would create better artwork:`;

    console.log('Calling Gemini API for prompt improvement...');

    let initialImprovedPrompt: string;

    try {
      const response = await llm.invoke([
        {
          role: "user",
          content: improvePrompt
        }
      ]);

      initialImprovedPrompt = String(response.content);
    } catch (apiError) {
      console.error('Gemini API error for prompt improvement:', apiError);

      // Provide fallback improved prompts based on style
      const fallbackImprovements = {
        cartoon: `A vibrant cartoon-style ${prompt} with bright colors, friendly expressions, and playful details that children will love to see`,
        watercolor: `A soft watercolor painting of ${prompt} with gentle flowing colors, dreamy textures, and artistic brush strokes`,
        realistic: `A detailed realistic artwork showing ${prompt} with lifelike textures, natural lighting, and beautiful composition`,
        comic: `A dynamic comic book style ${prompt} with bold outlines, action poses, and superhero-inspired visual effects`,
        pixel: `A retro pixel art version of ${prompt} with 8-bit style colors, blocky textures, and nostalgic gaming aesthetics`
      };

      initialImprovedPrompt = fallbackImprovements[style as keyof typeof fallbackImprovements] ||
                             `An enhanced artistic version of ${prompt} with beautiful details, vibrant colors, and creative visual elements`;

      console.log('Using fallback improved prompt due to API error:', initialImprovedPrompt);
    }

    const outCheck = await moderateContent(initialImprovedPrompt, 'text');
    const improvedPrompt = outCheck.isAppropriate ? initialImprovedPrompt : "Let's think of a different artistic idea!";
    console.log('Improved prompt:', improvedPrompt);

    return NextResponse.json({
      success: true,
      improvedPrompt,
      originalPrompt: prompt
    });

  } catch (error) {
    console.error('Error improving prompt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to improve prompt' },
      { status: 500 }
    );
  }
} 



