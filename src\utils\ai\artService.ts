// TODO: Replace with custom backend API calls
// import { callAIService } from '../api/apiClient';

// TODO: Replace this with your custom art AI API endpoint
// const callCustomArtAI = async (params: { prompt: string; type: string }): Promise<string> => {
//   // Placeholder for future custom backend integration
//   try {
//     console.log('[PLACEHOLDER] Would call art AI service with:', params);
//     return "Here's a wonderful art idea: Create a colorful landscape with rolling hills, a bright sun, and maybe some friendly animals playing in the meadow!";
//   } catch (error) {
//     console.error('Error calling custom art AI service:', error);
//     return "Let's create some beautiful art! What would you like to draw or paint today?";
//   }
// };

import { moderateContent, handleInappropriateContent } from './contentModeration';
import { toast } from 'sonner';
import { isBlocked, incrementStrike, getRemainingBlockMinutes, clearDevelopmentBlocks } from './strikeTracker';
// Auto-reset content safety blocks in development
import './resetContentSafety';

/**
 * Generate a completely original art idea using AI
 */
export const generateArtIdea = async (style: string = 'realistic'): Promise<string> => {
  try {
    // Clear any development blocks
    clearDevelopmentBlocks();

    if (isBlocked()) {
      toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
      throw new Error('Blocked');
    }
    console.log('Generating original art idea using AI...');
    
    const response = await fetch('/api/art/generate-idea', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ style })
    });

    if (!response.ok) {
      // Local strike handle
      const errData = await response.json().catch(() => ({}));
      if (errData?.error) toast.warning(errData.error);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errData.error || 'Failed to generate art idea');
    }

    const data = await response.json();
    
    if (!data.success || !data.idea) {
      throw new Error(data.error || 'Failed to get art idea');
    }

    console.log('Successfully generated art idea:', data.idea);
    return data.idea;
  } catch (error) {
    console.error("Error generating art idea:", error);
    return "Let's draw something fun! Describe what you'd like to create.";
  }
};

/**
 * Improve an art prompt using AI
 */
export const improveArtPrompt = async (originalPrompt: string, style: string = 'cartoon'): Promise<string> => {
  try {
    if (isBlocked()) {
      toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
      throw new Error('Blocked');
    }
    console.log('Improving art prompt using AI:', originalPrompt);
    
    // First-pass moderation (non-blocking): if the Supabase edge function is unavailable, continue
    try {
      const moderation = await moderateContent(originalPrompt, 'prompt');
      if (!moderation.isAppropriate) {
        // If the reason is just that safety could not be verified, skip blocking & toast
        if (
          moderation.reason &&
          moderation.reason.toLowerCase().includes('unable to verify content safety')
        ) {
          console.warn('[improveArtPrompt] moderation uncertain – proceeding anyway');
        } else {
          handleInappropriateContent('prompt improvement', moderation.reason);
          const strikeState = incrementStrike();
          if (strikeState.blockedUntil > Date.now()) {
            toast.error('Content safety: you are temporarily blocked for 5 minutes.');
          }
          throw new Error(moderation.reason || 'Content may not be appropriate');
        }
      }
    } catch (modErr) {
      console.warn('[improveArtPrompt] moderation service unavailable, skipping check:', modErr);
      // Proceed without blocking
    }

    const response = await fetch('/api/art/improve-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt: originalPrompt, style })
    });

    if (!response.ok) {
      const errData = await response.json().catch(() => ({}));
      if (errData?.error) toast.warning(errData.error);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errData.error || 'Failed to improve prompt');
    }

    const data = await response.json();
    
    if (!data.success || !data.improvedPrompt) {
      throw new Error(data.error || 'Failed to get improved prompt');
    }

    console.log('Successfully improved prompt:', data.improvedPrompt);
    return data.improvedPrompt;
  } catch (error) {
    console.error("Error improving art prompt:", error);
    // Return the original prompt if improvement fails
    return originalPrompt;
  }
};

/**
 * Generate artwork using AI
 */
export const generateArtwork = async (
  prompt: string, 
  style: string = 'realistic', 
  aspectRatio: string = '1:1'
): Promise<{ imageUrl: string; metadata: unknown }> => {
  try {
    if (isBlocked()) {
      toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
      throw new Error('Blocked');
    }
    console.log('Generating artwork using AI:', { prompt, style, aspectRatio });
    
    // Check if the prompt is appropriate
    const moderation = await moderateContent(prompt, 'prompt');
    if (!moderation.isAppropriate) {
      handleInappropriateContent('artwork', moderation.reason);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(moderation.reason || 'Content may not be appropriate');
    }

    const response = await fetch('/api/art/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        style,
        aspectRatio
      })
    });

    if (!response.ok) {
      const errData = await response.json().catch(() => ({}));
      if (errData?.error) toast.warning(errData.error);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errData.error || 'Failed to generate artwork');
    }

    const data = await response.json();
    
    if (!data.success || !data.imageUrl) {
      throw new Error(data.error || 'Failed to generate artwork');
    }

    console.log('Successfully generated artwork:', data.imageUrl);
    return {
      imageUrl: data.imageUrl,
      metadata: data.metadata
    };
  } catch (error) {
    console.error("Error generating artwork:", error);
    // Re-throw so caller can show placeholder; generatedImage should remain null
    throw error;
  }
};

/**
 * Generate art description (legacy function for backward compatibility)
 */
export const generateArtDescription = async (prompt: string): Promise<string> => {
  try {
    return await improveArtPrompt(prompt);
  } catch (error) {
    console.error("Error generating art description:", error);
    return prompt;
  }
};
