import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { GoogleGenAI } from '@google/genai';
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

export async function POST(request: Request) {
  try {
    const { prompt, style = 'realistic', aspectRatio = '1:1' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const styleKey = String(style).toLowerCase();

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!process.env.GOOGLE_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Google API key not configured' },
        { status: 500 }
      );
    }

    // Initialize image search tool for enhanced prompt generation
    const imageSearch = new GoogleImageSearchTool();
    let styledPrompt = prompt;

    // Use image search to enhance the prompt if available
    if (imageSearch.isAvailable()) {
      try {
        console.log(`[ArtGenerate] Using image search to enhance prompt for ${styleKey} style`);
        styledPrompt = await imageSearch.getEnhancedPromptFromSearch(prompt, styleKey);
        console.log(`[ArtGenerate] Enhanced prompt: ${styledPrompt}`);
      } catch (error) {
        console.error('[ArtGenerate] Image search enhancement failed, using fallback:', error);
        // Fall back to manual style enhancement
        styledPrompt = prompt;
      }
    } else {
      console.log('[ArtGenerate] Image search not available, using manual style enhancement');
    }

    // Apply additional style-specific enhancements
    switch (styleKey) {
      case 'cartoon':
        if (!styledPrompt.includes('cartoon') && !styledPrompt.includes('animated')) {
          styledPrompt += ', vibrant animated cartoon style, bold outlines, smooth colors, whimsical, cute, simple shapes, 2D animation feel';
        }
        break;
      case 'comic':
        if (!styledPrompt.includes('comic') && !styledPrompt.includes('graphic')) {
          styledPrompt += ', graphic novel comic book style, strong linework, halftone dots, vibrant flat colors, dynamic action, narrative panel, expressive characters';
        }
        break;
      case 'watercolor':
        if (!styledPrompt.includes('watercolor') && !styledPrompt.includes('painting')) {
          styledPrompt += ', delicate watercolor painting, soft ethereal tones, translucent washes, visible brushstrokes, blooming effect, serene, peaceful, hand-painted texture';
        }
        break;
      case 'pixel':
        if (!styledPrompt.includes('pixel') && !styledPrompt.includes('8-bit')) {
          styledPrompt += ', retro pixel art style, 8-bit, 16-bit, game sprite, chunky pixels, low resolution aesthetic, vibrant block colors, nostalgic';
        }
        break;
      case 'realistic':
        // Enhanced realistic style with stronger photographic terms
        if (!styledPrompt.includes('photograph') && !styledPrompt.includes('photo')) {
          styledPrompt += ', ultra-realistic photograph, professional photography, DSLR camera, natural lighting, sharp focus, high resolution, photojournalism style, authentic, genuine, lifelike, detailed textures, real world, actual photograph, not illustration, not cartoon, not drawing';
        }
        break;
      default: // base style
        if (!styledPrompt.includes('artistic') && !styledPrompt.includes('creative')) {
          styledPrompt += ', vibrant, imaginative, child-friendly artistic style';
        }
        break;
    }

    // Add child-friendly safety modifiers for all prompts
    styledPrompt += ', safe for children, positive, no violence, no scary content, educational, inspiring, bright colors, happy mood, fantasy elements';

    // Initialize Google GenAI
    const ai = new GoogleGenAI({
      apiKey: process.env.GOOGLE_API_KEY!,
    });

    const config = {
      responseModalities: ['IMAGE', 'TEXT'],
    };

    const model = 'gemini-2.0-flash-preview-image-generation';
    const contents = [
      {
        role: 'user' as const,
        parts: [
          {
            text: styledPrompt,
          },
        ],
      },
    ];

    // Generate image using Gemini
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let imageData: string | null = null;
    let mimeType: string | null = null;

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        imageData = inlineData.data || null;
        mimeType = inlineData.mimeType || null;
        break; // We only need the first image
      }
    }

    if (!imageData || !mimeType) {
      throw new Error('Image generation failed - no image data received');
    }

    // Convert base64 to data URL for direct use
    const imageUrl = `data:${mimeType};base64,${imageData}`;

    return NextResponse.json({
      success: true,
      imageUrl,
      metadata: {
        styledPrompt,
        prompt,
        style: styleKey,
        aspectRatio,
        imageSearchUsed: imageSearch.isAvailable(),
        enhancementMethod: imageSearch.isAvailable() ? 'image-search-enhanced' : 'manual-style-enhancement'
      }
    });
  } catch (e) {
    console.error('[api/art/generate] error', e);
    return NextResponse.json(
      { success: false, error: e instanceof Error ? e.message : 'failed' },
      { status: 500 }
    );
  }
} 