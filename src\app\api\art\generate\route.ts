import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { GoogleGenAI } from '@google/genai';
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

export async function POST(request: Request) {
  try {
    const { prompt, style = 'realistic', aspectRatio = '1:1' } = await request.json();

    console.log('🎨 [ART GENERATE] ===== NEW REQUEST =====');
    console.log('🎨 [ART GENERATE] Original prompt:', prompt);
    console.log('🎨 [ART GENERATE] Requested style:', style);
    console.log('🎨 [ART GENERATE] Aspect ratio:', aspectRatio);

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const styleKey = String(style).toLowerCase();
    console.log('🎨 [ART GENERATE] Processed style key:', styleKey);

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!process.env.GOOGLE_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Google API key not configured' },
        { status: 500 }
      );
    }

    // Initialize image search tool for enhanced prompt generation
    const imageSearch = new GoogleImageSearchTool();
    let styledPrompt = prompt;

    // Use image search to enhance the prompt if available
    if (imageSearch.isAvailable()) {
      try {
        console.log(`🔍 [ART GENERATE] Using image search to enhance prompt for ${styleKey} style`);
        styledPrompt = await imageSearch.getEnhancedPromptFromSearch(prompt, styleKey);
        console.log(`🔍 [ART GENERATE] Search-enhanced prompt: ${styledPrompt}`);
      } catch (error) {
        console.error('🔍 [ART GENERATE] Image search enhancement failed, using fallback:', error);
        // Fall back to manual style enhancement
        styledPrompt = prompt;
      }
    } else {
      console.log('🔍 [ART GENERATE] Image search not available, using manual style enhancement');
    }

    // Apply additional style-specific enhancements
    console.log(`🎨 [ART GENERATE] Applying style enhancements for: ${styleKey}`);
    switch (styleKey) {
      case 'cartoon':
        if (!styledPrompt.includes('cartoon') && !styledPrompt.includes('animated')) {
          styledPrompt += ', vibrant 2D cartoon animation style, Disney Pixar quality, smooth cel-shading, bright saturated colors, expressive character design, clean vector art, playful whimsical atmosphere, rounded friendly shapes, glossy finish, professional animation studio quality';
        }
        break;
      case 'comic':
        if (!styledPrompt.includes('comic') && !styledPrompt.includes('graphic')) {
          styledPrompt += ', dynamic comic book illustration, Marvel DC Comics style, bold black ink outlines, dramatic perspective, action-packed composition, vibrant flat colors, halftone dot patterns, speech bubble ready, superhero art style, graphic novel quality, professional comic artist work';
        }
        break;
      case 'watercolor':
        if (!styledPrompt.includes('watercolor') && !styledPrompt.includes('painting')) {
          styledPrompt += ', traditional watercolor painting, soft wet-on-wet technique, delicate color bleeding, transparent layered washes, visible paper texture, artistic brushwork, gentle color gradients, dreamy atmospheric effects, hand-painted authenticity, fine art gallery quality';
        }
        break;
      case 'pixel':
        if (!styledPrompt.includes('pixel') && !styledPrompt.includes('8-bit')) {
          styledPrompt += ', retro pixel art, 16-bit video game style, chunky square pixels, limited color palette, classic arcade aesthetic, nostalgic gaming vibes, crisp pixel-perfect edges, dithering effects, sprite-based design, indie game art quality, Super Nintendo era graphics';
        }
        break;
      case 'realistic':
        // Enhanced realistic style with stronger photographic terms
        if (!styledPrompt.includes('photograph') && !styledPrompt.includes('photo')) {
          styledPrompt += ', ultra-realistic photograph, professional DSLR photography, natural golden hour lighting, sharp focus depth of field, high resolution 4K quality, National Geographic style, authentic real-world scene, photojournalism excellence, genuine lifelike textures, not illustration, not cartoon, not digital art, actual photography';
        }
        break;
      default: // base style
        if (!styledPrompt.includes('artistic') && !styledPrompt.includes('creative')) {
          styledPrompt += ', vibrant artistic illustration, creative digital art, imaginative child-friendly style, colorful and engaging';
        }
        break;
    }
    console.log(`🎨 [ART GENERATE] Final styled prompt: ${styledPrompt}`);

    // Add child-friendly safety modifiers for all prompts
    styledPrompt += ', safe for children, positive, no violence, no scary content, educational, inspiring, bright colors, happy mood, fantasy elements';
    console.log(`🤖 [ART GENERATE] Final prompt being sent to AI: ${styledPrompt}`);

    // Initialize Google GenAI
    const ai = new GoogleGenAI({
      apiKey: process.env.GOOGLE_API_KEY!,
    });

    const config = {
      responseModalities: ['IMAGE', 'TEXT'],
    };

    const model = 'gemini-2.0-flash-preview-image-generation';
    const contents = [
      {
        role: 'user' as const,
        parts: [
          {
            text: styledPrompt,
          },
        ],
      },
    ];

    // Generate image using Gemini
    console.log(`🚀 [ART GENERATE] Sending request to AI model: ${model}`);
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });
    console.log(`📡 [ART GENERATE] Received response stream from AI model`);

    let imageData: string | null = null;
    let mimeType: string | null = null;

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        imageData = inlineData.data || null;
        mimeType = inlineData.mimeType || null;
        break; // We only need the first image
      }
    }

    if (!imageData || !mimeType) {
      console.error('❌ [ART GENERATE] Image generation failed - no image data received');
      throw new Error('Image generation failed - no image data received');
    }

    console.log(`✅ [ART GENERATE] Successfully generated image with mimeType: ${mimeType}, data length: ${imageData.length}`);

    // Convert base64 to data URL for direct use
    const imageUrl = `data:${mimeType};base64,${imageData}`;

    console.log(`🎯 [ART GENERATE] ===== GENERATION COMPLETE =====`);
    console.log(`🎯 [ART GENERATE] Style used: ${styleKey}`);
    console.log(`🎯 [ART GENERATE] Image search used: ${imageSearch.isAvailable()}`);
    console.log(`🎯 [ART GENERATE] Enhancement method: ${imageSearch.isAvailable() ? 'image-search-enhanced' : 'manual-style-enhancement'}`);

    return NextResponse.json({
      success: true,
      imageUrl,
      metadata: {
        styledPrompt,
        prompt,
        style: styleKey,
        aspectRatio,
        imageSearchUsed: imageSearch.isAvailable(),
        enhancementMethod: imageSearch.isAvailable() ? 'image-search-enhanced' : 'manual-style-enhancement'
      }
    });
  } catch (e) {
    console.error('[api/art/generate] error', e);
    return NextResponse.json(
      { success: false, error: e instanceof Error ? e.message : 'failed' },
      { status: 500 }
    );
  }
} 