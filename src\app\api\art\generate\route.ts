import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { GoogleGenAI } from '@google/genai';
import mime from 'mime';

export async function POST(request: Request) {
  try {
    const { prompt, style = 'realistic', aspectRatio = '1:1' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const styleKey = String(style).toLowerCase();

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!process.env.GOOGLE_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Google API key not configured' },
        { status: 500 }
      );
    }

    // Style modifier
    let styledPrompt = prompt;

    switch (styleKey) {
      case 'cartoon':
        styledPrompt += ', vibrant animated cartoon style, bold outlines, smooth colors, whimsical, cute, simple shapes, 2D animation feel';
        break;
      case 'comic':
        styledPrompt += ', graphic novel comic book style, strong linework, halftone dots, vibrant flat colors, dynamic action, narrative panel, expressive characters';
        break;
      case 'watercolor':
        styledPrompt += ', delicate watercolor painting, soft ethereal tones, translucent washes, visible brushstrokes, blooming effect, serene, peaceful, hand-painted texture';
        break;
      case 'pixel':
        styledPrompt += ', retro pixel art style, 8-bit, 16-bit, game sprite, chunky pixels, low resolution aesthetic, vibrant block colors, nostalgic';
        break;
      case 'realistic':
        styledPrompt += ', ultra-realistic photograph, cinematic, intricately detailed, lifelike, natural lighting, shallow depth of field, sharp focus, professional photo';
        break;
      default: // base style
        styledPrompt += ', vibrant, imaginative, child-friendly artistic style';
        break;
    }

    // Add child-friendly safety modifiers for all prompts
    styledPrompt += ', safe for children, positive, no violence, no scary content, educational, inspiring, bright colors, happy mood, fantasy elements';

    // Initialize Google GenAI
    const ai = new GoogleGenAI({
      apiKey: process.env.GOOGLE_API_KEY!,
    });

    const config = {
      responseModalities: ['IMAGE', 'TEXT'],
    };

    const model = 'gemini-2.0-flash-preview-image-generation';
    const contents = [
      {
        role: 'user' as const,
        parts: [
          {
            text: styledPrompt,
          },
        ],
      },
    ];

    // Generate image using Gemini
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let imageData: string | null = null;
    let mimeType: string | null = null;

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        imageData = inlineData.data || null;
        mimeType = inlineData.mimeType || null;
        break; // We only need the first image
      }
    }

    if (!imageData || !mimeType) {
      throw new Error('Image generation failed - no image data received');
    }

    // Convert base64 to data URL for direct use
    const imageUrl = `data:${mimeType};base64,${imageData}`;

    return NextResponse.json({
      success: true,
      imageUrl,
      metadata: { styledPrompt, prompt, style: styleKey, aspectRatio }
    });
  } catch (e) {
    console.error('[api/art/generate] error', e);
    return NextResponse.json(
      { success: false, error: e instanceof Error ? e.message : 'failed' },
      { status: 500 }
    );
  }
} 