#!/usr/bin/env node

/**
 * Test script for the enhanced image generation system
 * 
 * This script tests all the updated API endpoints to ensure:
 * 1. Google image search integration works
 * 2. Gemini image generation works
 * 3. Style-specific enhancements work correctly
 * 4. Realistic style produces photographic results
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// Test cases for different styles and prompts
const testCases = [
  {
    endpoint: '/api/art/generate',
    name: 'Realistic Style - Landscape',
    payload: {
      prompt: 'a beautiful sunset over mountains',
      style: 'realistic',
      aspectRatio: '16:9'
    },
    expectedKeywords: ['photograph', 'professional', 'natural lighting', 'DSLR']
  },
  {
    endpoint: '/api/art/generate',
    name: 'Cartoon Style - Character',
    payload: {
      prompt: 'a friendly dragon',
      style: 'cartoon',
      aspectRatio: '1:1'
    },
    expectedKeywords: ['cartoon', 'animated', 'vibrant', 'colorful']
  },
  {
    endpoint: '/api/art/generate',
    name: 'Comic Style - Action',
    payload: {
      prompt: 'superhero flying through city',
      style: 'comic',
      aspectRatio: '4:3'
    },
    expectedKeywords: ['comic', 'graphic', 'dynamic', 'bold']
  },
  {
    endpoint: '/api/art/generate-idea',
    name: 'Art Idea Generation - Watercolor',
    payload: {
      style: 'watercolor'
    },
    expectedKeywords: ['watercolor', 'soft', 'painting']
  },
  {
    endpoint: '/api/art/improve-prompt',
    name: 'Prompt Improvement - Pixel Art',
    payload: {
      prompt: 'cute robot',
      style: 'pixel'
    },
    expectedKeywords: ['pixel', 'retro', '8-bit']
  },
  {
    endpoint: '/api/generate-coloring',
    name: 'Coloring Page Generation',
    payload: {
      prompt: 'happy flower'
    },
    expectedKeywords: ['line art', 'coloring', 'outlines']
  }
];

/**
 * Test a single API endpoint
 */
async function testEndpoint(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log(`📍 Endpoint: ${testCase.endpoint}`);
  console.log(`📝 Payload:`, JSON.stringify(testCase.payload, null, 2));

  try {
    const response = await fetch(`${BASE_URL}${testCase.endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCase.payload)
    });

    const data = await response.json();

    if (!response.ok) {
      console.log(`❌ Failed: ${data.error || 'Unknown error'}`);
      return false;
    }

    console.log(`✅ Success: ${response.status}`);

    // Check for expected functionality
    if (testCase.endpoint === '/api/art/generate') {
      if (data.imageUrl && data.imageUrl.startsWith('data:image/')) {
        console.log(`🖼️  Image generated: ${data.imageUrl.substring(0, 50)}...`);
      }
      
      if (data.metadata) {
        console.log(`📊 Metadata:`, {
          style: data.metadata.style,
          imageSearchUsed: data.metadata.imageSearchUsed,
          enhancementMethod: data.metadata.enhancementMethod
        });
        
        // Check if prompt contains expected keywords
        const styledPrompt = data.metadata.styledPrompt?.toLowerCase() || '';
        const foundKeywords = testCase.expectedKeywords.filter(keyword => 
          styledPrompt.includes(keyword.toLowerCase())
        );
        
        if (foundKeywords.length > 0) {
          console.log(`🎯 Style keywords found: ${foundKeywords.join(', ')}`);
        } else {
          console.log(`⚠️  No expected style keywords found in prompt`);
        }
      }
    } else if (testCase.endpoint === '/api/art/generate-idea') {
      if (data.idea) {
        console.log(`💡 Generated idea: "${data.idea}"`);
      }
    } else if (testCase.endpoint === '/api/art/improve-prompt') {
      if (data.improvedPrompt) {
        console.log(`📝 Original: "${data.originalPrompt}"`);
        console.log(`✨ Improved: "${data.improvedPrompt}"`);
      }
    } else if (testCase.endpoint === '/api/generate-coloring') {
      if (data.image && data.image.startsWith('data:image/')) {
        console.log(`🎨 Coloring page generated: ${data.image.substring(0, 50)}...`);
      }
    }

    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return false;
  }
}

/**
 * Test image search tool availability
 */
async function testImageSearchAvailability() {
  console.log('\n🔍 Testing Image Search Availability...');
  
  const hasGoogleSearchKey = !!process.env.GOOGLE_SEARCH_API_KEY;
  const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
  
  console.log(`🔑 GOOGLE_API_KEY: ${hasGoogleApiKey ? '✅ Available' : '❌ Missing'}`);
  console.log(`🔍 GOOGLE_SEARCH_API_KEY: ${hasGoogleSearchKey ? '✅ Available' : '❌ Missing'}`);
  
  if (!hasGoogleApiKey) {
    console.log('⚠️  Image generation will not work without GOOGLE_API_KEY');
  }
  
  if (!hasGoogleSearchKey) {
    console.log('⚠️  Image search enhancement will fall back to manual style enhancement');
  }
  
  return hasGoogleApiKey;
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Enhanced Image Generation System Test Suite');
  console.log('='.repeat(50));

  // Check environment setup
  const canRunTests = await testImageSearchAvailability();
  
  if (!canRunTests) {
    console.log('\n❌ Cannot run tests without GOOGLE_API_KEY');
    console.log('Please set up your environment variables and try again.');
    process.exit(1);
  }

  // Run all test cases
  let passed = 0;
  let failed = 0;

  for (const testCase of testCases) {
    const success = await testEndpoint(testCase);
    if (success) {
      passed++;
    } else {
      failed++;
    }
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! The enhanced image generation system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testEndpoint, testImageSearchAvailability };
