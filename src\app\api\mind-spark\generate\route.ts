import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.9,
  apiKey: GOOGLE_API_KEY || "",
});

interface GenerateQuestionRequest {
  age_group: string;
  category?: string;
  difficulty?: string;
  question_type?: 'open' | 'mcq';
}

interface MCQResponse {
  question: string;
  options: string[];
  correct_answer: string;
  explanation: string;
}

const ageGroupPrompts = {
    "5-7": {
      description: "5-7 year olds (early elementary)",
      characteristics: "Generate questions based on general knowledge, imaginative scenarios, and simple observations. Focus on topics like animals, colors, and everyday objects.",
      examples: [
          "What color is the sky on a sunny day?",
          "If you mix red and yellow paint, what color do you get?"
      ]
    },
    "8-10": {
        description: "8-10 year olds (elementary)",
        characteristics: "Introduce slightly more complex topics like multiplication, basic science concepts (e.g., 'What planet is known as the Red Planet?'), and geography. Questions should encourage logical thinking.",
        examples: [
            "If a car travels at 50 miles per hour, how far will it go in 3 hours?",
            "What is the largest ocean on Earth?"
        ]
    },
    "11-13": {
        description: "11-13 year olds (middle school)",
        characteristics: "Focus on problem-solving, historical events, and more advanced scientific principles. Questions can require multiple steps or abstract thinking.",
        examples: [
            "Who was the first person to walk on the moon?",
            "What is the powerhouse of the cell?"
        ]
    },
    "14-16": {
        description: "14-16 year olds (high school)",
        characteristics: "Introduce questions related to current events, civics, and more complex mathematical problems. Encourage critical thinking about real-world applications.",
        examples: [
            "What are the three branches of the U.S. government?",
            "If a stock price increases by 15%, what is its new price?"
        ]
    },
    "17+": {
        description: "17+ year olds (young adults)",
        characteristics: "Generate questions based on current global events, real-world scenarios, and advanced general studies. Questions should test critical thinking, ethical reasoning, and practical knowledge.",
        examples: [
            "Discuss the economic impact of renewable energy adoption.",
            "What are the ethical implications of AI in the workplace?"
        ]
    }
};


async function generateMCQWithGemini(age_group: string, category: string, difficulty: string): Promise<MCQResponse> {
  if (!GOOGLE_API_KEY) {
    throw new Error('Gemini API key not configured');
  }

  const ageInfo = ageGroupPrompts[age_group as keyof typeof ageGroupPrompts];
  if (!ageInfo) {
    throw new Error(`Invalid age group: ${age_group}`);
  }

    const educationalCategories = ["Math", "Science", "History", "Geography", "Logic", "Computer Science"];
    const selectedCategory = educationalCategories.includes(category) ? category : educationalCategories[Math.floor(Math.random() * educationalCategories.length)];

    const prompt = `Please generate a multiple-choice question that is both educational and engaging for a child in the ${age_group} age group. The question should be from the **${selectedCategory}** category and have a **${difficulty}** difficulty level.

Here are some details about the target audience:
- **Age Group:** ${ageInfo.description}
- **Characteristics:** ${ageInfo.characteristics}

**Requirements:**
1.  The question must be age-appropriate and easy to understand.
2.  Provide 4 answer options (A, B, C, D).
3.  Ensure there is only ONE correct answer.
4.  The question should be designed to make learning fun.
5.  Include a short, one-line explanation for the correct answer.

**Output Format (JSON only):**
{
  "question": "Your question here?",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct_answer": "The correct option",
  "explanation": "A brief, one-line explanation of the answer."
}

**Example for ${ageInfo.description}:**
*Good Example:* "If a cat runs at 10 miles per hour, how long will it take to run 20 miles?" (Math)
*Bad Example:* "What is the airspeed velocity of an unladen swallow?" (Too obscure)

Please provide the output in a clean JSON format, without any extra text or explanations.`;

  try {
    const response = await llm.invoke(prompt);
    console.log('Gemini MCQ Response:', response);

    if (!response.content) {
      throw new Error('No content generated by Gemini');
    }

    let content = '';
    if (typeof response.content === 'string') {
      content = response.content;
    } else if (Array.isArray(response.content)) {
      const firstText = response.content
        .map((item: unknown) => {
          if (typeof item === 'string') return item;
          if (item && typeof item === 'object' && 'text' in item && typeof item.text === 'string') return item.text;
          return null;
        })
        .find((text: string | null) => !!text);
      if (firstText) content = firstText;
    }

    if (!content) {
      throw new Error('No valid content generated by Gemini');
    }

    // Clean up the response to extract JSON
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    const mcqData = JSON.parse(jsonMatch[0]);

    // Validate the response structure
    if (!mcqData.question || !mcqData.options || !mcqData.correct_answer || !mcqData.explanation) {
      throw new Error('Invalid MCQ structure from Gemini');
    }

    if (!Array.isArray(mcqData.options) || mcqData.options.length !== 4) {
      throw new Error('MCQ must have exactly 4 options');
    }

    if (!mcqData.options.includes(mcqData.correct_answer)) {
      throw new Error('Correct answer must be one of the options');
    }

    return mcqData;

  } catch (error) {
    console.error('Error calling Gemini API for MCQ:', error);
    throw error;
  }
}

// Get or create user performance for adaptive difficulty
async function getUserPerformance(userId: string, ageGroup: string) {
  let performance = await prisma.mindSparkPerformance.findUnique({
    where: {
      user_id_age_group: {
        user_id: userId,
        age_group: ageGroup
      }
    }
  });

  if (!performance) {
    performance = await prisma.mindSparkPerformance.create({
      data: {
        user_id: userId,
        age_group: ageGroup,
        current_difficulty: 'medium',
        consecutive_wrong: 0,
        consecutive_right: 0,
        total_questions: 0,
        total_correct: 0
      }
    });
  }

  return performance;
}

// Determine difficulty based on user performance
function getAdaptiveDifficulty(performance: {
  consecutive_wrong: number;
  consecutive_right: number;
  current_difficulty: string;
}): string {
  // If user gets 3+ wrong in a row, reduce difficulty
  if (performance.consecutive_wrong >= 3) {
    if (performance.current_difficulty === 'hard') return 'medium';
    if (performance.current_difficulty === 'medium') return 'easy';
    return 'easy';
  }

  // If user gets 5+ right in a row, increase difficulty
  if (performance.consecutive_right >= 5) {
    if (performance.current_difficulty === 'easy') return 'medium';
    if (performance.current_difficulty === 'medium') return 'hard';
    return 'hard';
  }

  return performance.current_difficulty;
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const body: GenerateQuestionRequest = await request.json();
    const { age_group, category = 'general', question_type = 'mcq' } = body;

    // Validate age group
    if (!ageGroupPrompts[age_group as keyof typeof ageGroupPrompts]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid age group. Must be one of: 5-7, 8-10, 11-13, 14-16, 17+'
        },
        { status: 400 }
      );
    }

    // Get user performance for adaptive difficulty
    const performance = await getUserPerformance(user.id, age_group);
    const adaptiveDifficulty = getAdaptiveDifficulty(performance);

    console.log(`🤖 [MIND-SPARK-GENERATE] Generating ${question_type} question for age group: ${age_group}, category: ${category}, adaptive difficulty: ${adaptiveDifficulty}`);
    console.log(`📊 [PERFORMANCE] User stats - Wrong: ${performance.consecutive_wrong}, Right: ${performance.consecutive_right}, Total: ${performance.total_questions}`);

    // Generate MCQ question using Gemini
    const mcqData = await generateMCQWithGemini(age_group, category, adaptiveDifficulty);

    console.log(`✨ [MIND-SPARK-GENERATE] Generated MCQ: ${mcqData.question}`);

    // Save to database
    const savedQuestion = await prisma.mindSparkQuestion.create({
      data: {
        question: mcqData.question,
        age_group,
        difficulty: adaptiveDifficulty,
        category,
        question_type,
        options: mcqData.options,
        correct_answer: mcqData.correct_answer,
        is_ai_generated: true,
      }
    });

    console.log(`💾 [MIND-SPARK-GENERATE] Saved MCQ to database with ID: ${savedQuestion.id}`);

    return NextResponse.json({
      success: true,
      message: 'MCQ question generated and saved successfully',
      question: {
        id: savedQuestion.id,
        question: savedQuestion.question,
        age_group: savedQuestion.age_group,
        category: savedQuestion.category,
        difficulty: savedQuestion.difficulty,
        question_type: savedQuestion.question_type,
        options: savedQuestion.options,
        correct_answer: savedQuestion.correct_answer,
        explanation: mcqData.explanation,
        is_ai_generated: savedQuestion.is_ai_generated,
        created_at: savedQuestion.created_at
      },
      performance: {
        current_difficulty: adaptiveDifficulty,
        consecutive_wrong: performance.consecutive_wrong,
        consecutive_right: performance.consecutive_right,
        total_questions: performance.total_questions,
        total_correct: performance.total_correct
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-GENERATE] Error generating question:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate question',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
