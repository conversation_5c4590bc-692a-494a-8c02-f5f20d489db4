import axios from 'axios';

export interface ImageSearchResult {
  title: string;
  imageUrl: string;
  source: string;
  snippet?: string;
}

interface SerperImageResult {
  title: string;
  imageUrl: string;
  imageWidth: number;
  imageHeight: number;
  thumbnailUrl: string;
  thumbnailWidth: number;
  thumbnailHeight: number;
  source: string;
  domain: string;
  link: string;
  snippet?: string;
}

/**
 * Google Image Search Tool for finding reference images to enhance AI image generation
 * 
 * This tool searches for images that match the user's prompt and style to provide
 * better context and reference material for the Gemini image generation model.
 */
export class GoogleImageSearchTool {
  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.GOOGLE_SEARCH_API_KEY || '';
    
    if (!this.apiKey) {
      console.error('No Google Search API key provided. Image search functionality will not work.');
    }
  }

  /**
   * Search for images based on prompt and style
   */
  async searchImages(prompt: string, style: string = 'realistic', limit: number = 5): Promise<ImageSearchResult[]> {
    try {
      if (!this.apiKey) {
        console.error('Image search aborted: No API key available');
        return [];
      }

      // Create style-specific search query
      const searchQuery = this.buildSearchQuery(prompt, style);
      console.log(`[ImageSearch] Searching for: "${searchQuery}"`);

      const response = await axios.post(
        'https://google.serper.dev/images',
        { 
          q: searchQuery,
          num: limit,
          safe: 'active' // Enable safe search for child-friendly content
        },
        {
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      const results = response.data.images || [];
      const imageResults = results.slice(0, limit).map((result: SerperImageResult) => ({
        title: result.title,
        imageUrl: result.imageUrl,
        source: result.source,
        snippet: result.snippet
      }));

      console.log(`[ImageSearch] Found ${imageResults.length} images for "${searchQuery}"`);
      return imageResults;
    } catch (error) {
      console.error('Image search error:', error);
      return [];
    }
  }

  /**
   * Build search query based on prompt and style
   */
  private buildSearchQuery(prompt: string, style: string): string {
    const basePrompt = prompt.trim();
    
    switch (style.toLowerCase()) {
      case 'realistic':
        return `${basePrompt} high quality photograph professional photography realistic photo`;
      case 'cartoon':
        return `${basePrompt} cartoon illustration animated style colorful`;
      case 'comic':
        return `${basePrompt} comic book art graphic novel illustration`;
      case 'watercolor':
        return `${basePrompt} watercolor painting artistic soft colors`;
      case 'pixel':
        return `${basePrompt} pixel art 8-bit retro game style`;
      default:
        return `${basePrompt} art illustration creative`;
    }
  }

  /**
   * Get enhanced prompt suggestions based on image search results
   */
  async getEnhancedPromptFromSearch(prompt: string, style: string): Promise<string> {
    try {
      const searchResults = await this.searchImages(prompt, style, 3);
      
      if (searchResults.length === 0) {
        return prompt; // Return original if no search results
      }

      // Extract common themes and descriptive elements from search results
      const descriptions = searchResults
        .map(result => result.title + ' ' + (result.snippet || ''))
        .join(' ')
        .toLowerCase();

      // Enhance prompt based on style and search insights
      return this.enhancePromptWithSearchInsights(prompt, style, descriptions);
    } catch (error) {
      console.error('Error enhancing prompt with search:', error);
      return prompt;
    }
  }

  /**
   * Enhance prompt with insights from search results
   */
  private enhancePromptWithSearchInsights(prompt: string, style: string, searchDescriptions: string): string {
    const basePrompt = prompt.trim();
    
    // Extract relevant descriptive words from search results
    const qualityWords = this.extractQualityWords(searchDescriptions, style);
    
    switch (style.toLowerCase()) {
      case 'realistic':
        return `${basePrompt}, ${qualityWords.join(', ')}, professional photography, high resolution, natural lighting, sharp focus, detailed, photorealistic`;
      case 'cartoon':
        return `${basePrompt}, ${qualityWords.join(', ')}, vibrant colors, smooth animation style, clean lines, cheerful`;
      case 'comic':
        return `${basePrompt}, ${qualityWords.join(', ')}, bold lines, dynamic composition, graphic novel style`;
      case 'watercolor':
        return `${basePrompt}, ${qualityWords.join(', ')}, soft brushstrokes, flowing colors, artistic texture`;
      case 'pixel':
        return `${basePrompt}, ${qualityWords.join(', ')}, retro gaming style, blocky pixels, nostalgic`;
      default:
        return `${basePrompt}, ${qualityWords.join(', ')}, creative artistic style`;
    }
  }

  /**
   * Extract quality and descriptive words from search results
   */
  private extractQualityWords(descriptions: string, style: string): string[] {
    const words: string[] = [];
    
    // Common quality indicators
    const qualityTerms = [
      'beautiful', 'stunning', 'amazing', 'gorgeous', 'elegant', 'magnificent',
      'detailed', 'intricate', 'fine', 'exquisite', 'masterpiece', 'artistic'
    ];
    
    // Style-specific terms
    const styleTerms: Record<string, string[]> = {
      realistic: ['natural', 'lifelike', 'authentic', 'genuine', 'real', 'actual'],
      cartoon: ['colorful', 'bright', 'cheerful', 'fun', 'playful', 'whimsical'],
      comic: ['dynamic', 'bold', 'dramatic', 'action', 'heroic', 'powerful'],
      watercolor: ['soft', 'gentle', 'flowing', 'delicate', 'ethereal', 'dreamy'],
      pixel: ['retro', 'classic', 'nostalgic', 'vintage', 'blocky', 'simple']
    };

    // Extract relevant terms
    qualityTerms.forEach(term => {
      if (descriptions.includes(term)) {
        words.push(term);
      }
    });

    const relevantStyleTerms = styleTerms[style.toLowerCase()] || [];
    relevantStyleTerms.forEach(term => {
      if (descriptions.includes(term)) {
        words.push(term);
      }
    });

    // Return unique words, limited to avoid overly long prompts
    return [...new Set(words)].slice(0, 5);
  }

  /**
   * Check if image search is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }
}
