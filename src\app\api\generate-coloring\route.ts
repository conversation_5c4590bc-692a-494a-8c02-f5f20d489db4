import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';
import { GoogleImageSearchTool } from '@/lib/ai/tools/imageSearchTool';

export async function POST(req: NextRequest) {
  const { prompt } = await req.json();
  console.log('🔥 API: Received prompt:', prompt);
  
  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
  }

  if (!process.env.GOOGLE_API_KEY) {
    return NextResponse.json({ error: 'Google API key not configured' }, { status: 500 });
  }

  // Initialize image search for better coloring page generation
  const imageSearch = new GoogleImageSearchTool();
  let enhancedPrompt = prompt;

  // Use image search to enhance the coloring page prompt
  if (imageSearch.isAvailable()) {
    try {
      console.log('🔥 API: Using image search to enhance coloring page prompt');
      const searchResults = await imageSearch.searchImages(`${prompt} coloring page line art`, 'cartoon', 3);
      if (searchResults.length > 0) {
        const searchContext = searchResults
          .map(result => result.title + ' ' + (result.snippet || ''))
          .join(' ')
          .toLowerCase();

        // Extract relevant descriptive elements for coloring pages
        const descriptiveWords = searchContext.match(/\b(simple|clean|outline|line|art|drawing|sketch|cute|friendly|fun)\b/g) || [];
        if (descriptiveWords.length > 0) {
          enhancedPrompt = `${prompt}, ${[...new Set(descriptiveWords)].slice(0, 3).join(', ')}`;
        }
      }
    } catch (error) {
      console.error('🔥 API: Image search enhancement failed:', error);
    }
  }

  // Create a coloring page prompt optimized for Gemini
  const coloringPrompt = `professional coloring book line art, bold black outlines of ${enhancedPrompt}, thick consistent line weight, completely enclosed shapes, clean vector style, minimal interior details, pure white background, high contrast black lines, simple geometric forms, child-friendly design, easy to color within lines, no gaps in outlines, smooth curves, perfect for coloring`;

  console.log('🔥 API: Enhanced coloring prompt:', coloringPrompt);

  // Initialize Google GenAI
  const ai = new GoogleGenAI({
    apiKey: process.env.GOOGLE_API_KEY!,
  });

  const config = {
    responseModalities: ['IMAGE', 'TEXT'],
  };

  const model = 'gemini-2.0-flash-preview-image-generation';
  const contents = [
    {
      role: 'user' as const,
      parts: [
        {
          text: coloringPrompt,
        },
      ],
    },
  ];

  try {
    // Generate coloring page using Gemini
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let imageData: string | null = null;
    let mimeType: string | null = null;

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        imageData = inlineData.data || null;
        mimeType = inlineData.mimeType || null;
        break; // We only need the first image
      }
    }

    if (!imageData || !mimeType) {
      throw new Error('Coloring page generation failed - no image data received');
    }

    // Convert base64 to data URL for direct use
    const imageUrl = `data:${mimeType};base64,${imageData}`;

    console.log('🔥 API: Successfully generated coloring page');
    return NextResponse.json({ image: imageUrl });

  } catch (err) {
    console.error('🔥 API: Gemini API error:', err);

    // Provide fallback placeholder coloring page
    const fallbackMessage = `Sorry, we couldn't generate a coloring page for "${prompt}" right now. Please try again later or try a different prompt like "cute cat", "happy flower", or "friendly robot".`;

    return NextResponse.json({
      error: 'Failed to generate image',
      details: err instanceof Error ? err.message : 'Unknown error',
      fallbackMessage
    }, { status: 500 });
  }
} 