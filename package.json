{"name": "spark-new", "version": "0.1.0", "private": true, "scripts": {"dev": "prisma generate && next dev --turbopack", "dev:quiet": "prisma generate && NEXT_LOGGING=false next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@google/genai": "^1.12.0", "@langchain/community": "^0.3.48", "@langchain/core": "^0.3.62", "@langchain/google-genai": "^0.2.14", "@langchain/groq": "^0.2.3", "@langchain/langgraph": "^0.3.7", "@prisma/client": "^6.9.0", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@studio-freight/lenis": "^1.0.42", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/typography": "^0.5.16", "@types/nodemailer": "^6.4.17", "@types/react-speech-recognition": "^3.9.6", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "duck-duck-scrape": "^2.2.7", "euri": "^0.1.0", "framer-motion": "^12.16.0", "lenis": "^1.3.4", "lucide-react": "^0.513.0", "mime": "^4.0.7", "next": "15.3.3", "nodemailer": "^7.0.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-h5-audio-player": "^3.10.0", "react-markdown": "^10.1.0", "react-speech-recognition": "^4.0.1", "remark-gfm": "^4.0.1", "replicate": "^1.0.1", "resend": "^4.7.0", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}