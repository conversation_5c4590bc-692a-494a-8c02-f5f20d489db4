
export interface Question {
  id: string;
  question: string;
  age_group: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  is_ai_generated: boolean;
  question_type: 'open' | 'mcq';
  options?: string[]; // For MCQ questions
  correct_answer?: string; // For MCQ questions
  created_at: string;
  explanation?: string;
  is_answered?: boolean;
  user_answer?: {
    id: string;
    answer: string;
    is_correct?: boolean; // For MCQ answers
    answered_at: string;
  } | null;
}

export interface MCQOption {
  text: string;
  isCorrect: boolean;
}

export interface UserPerformance {
  id: string;
  user_id: string;
  age_group: string;
  current_difficulty: 'easy' | 'medium' | 'hard';
  consecutive_wrong: number;
  consecutive_right: number;
  total_questions: number;
  total_correct: number;
  last_updated: string;
}

export type AgeGroup = '5-7' | '8-10' | '11-13' | '14-16' | '17+';

export interface MindSparkProps {
  userAge?: number;
  safeMode?: boolean;
  toolType?: 'general' | 'stem' | 'language' | 'story' | 'art' | 'game' | 'music';
}
