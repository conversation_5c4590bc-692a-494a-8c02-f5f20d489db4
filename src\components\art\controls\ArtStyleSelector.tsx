import React from 'react';
import { Button } from '@/components/ui/button';
import { ArtStyleType } from '@/hooks/art/useArtStyle';

interface ArtStyleSelectorProps {
  artStyle: ArtStyleType;
  setArtStyle: (style: ArtStyleType) => void;
}

const ArtStyleSelector = ({ artStyle, setArtStyle }: ArtStyleSelectorProps) => {
  const handleStyleChange = (style: ArtStyleType) => {
    console.log(`🎨 [FRONTEND] Style changed from ${artStyle} to ${style}`);
    setArtStyle(style);
  };

  return (
    <div className="space-y-4">
      <div className="block text-lg font-medium text-center">
        Select a Style
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        <Button
          variant={artStyle === 'cartoon' ? "default" : "outline"}
          size="sm"
          onClick={() => handleStyleChange('cartoon')}
          className={artStyle === 'cartoon' ? "bg-littlespark-teal" : ""}
        >
          Cartoon
        </Button>
        <Button
          variant={artStyle === 'realistic' ? "default" : "outline"}
          size="sm"
          onClick={() => handleStyleChange('realistic')}
          className={artStyle === 'realistic' ? "bg-littlespark-blue" : ""}
        >
          Realistic
        </Button>
        <Button
          variant={artStyle === 'comic' ? "default" : "outline"}
          size="sm"
          onClick={() => handleStyleChange('comic')}
          className={artStyle === 'comic' ? "bg-littlespark-lavender" : ""}
        >
          Comic Book
        </Button>
        <Button
          variant={artStyle === 'watercolor' ? "default" : "outline"}
          size="sm"
          onClick={() => handleStyleChange('watercolor')}
          className={artStyle === 'watercolor' ? "bg-littlespark-yellow" : ""}
        >
          Watercolor
        </Button>
        <Button
          variant={artStyle === 'pixel' ? "default" : "outline"}
          size="sm"
          onClick={() => handleStyleChange('pixel')}
          className={artStyle === 'pixel' ? "bg-littlespark-orange" : ""}
        >
          Pixel Art
        </Button>
      </div>
    </div>
  );
};

export default ArtStyleSelector;
