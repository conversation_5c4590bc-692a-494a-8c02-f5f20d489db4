"use client";
import React from "react";
import ArtHeader from "@/components/art/ArtHeader";
import ArtCreatorContent from "@/components/art/ArtCreatorContent";
import {
  useArtGeneration,
  useLearningMode,
  usePortfolioSave,
  useArtStyle,
} from "@/hooks/art";
import { useArtPrompt } from "@/hooks/art/useArtPrompt";

interface ArtCreatorProps {
  challengeData?: {
    title: string;
    description: string;
    category: string;
    instructions?: string;
    learningObjectives?: Array<{objective: string}>;
    materials?: Array<{material: string}>;
    estimatedTime?: number;
    difficulty?: string;
  };
}

const ArtCreator = ({ challengeData }: ArtCreatorProps) => {
  // Import art style hook
  const { artStyle, setArtStyle } = useArtStyle();

  // Extract art prompt functionality (needs artStyle)
  const {
    prompt,
    setPrompt,
    title,
    setTitle,
    isGeneratingIdea,
    isImprovingPrompt,
    handleGenerateIdea,
    handleImprovePrompt,
  } = useArtPrompt(artStyle);

  // Extract art generation functionality
  const {
    isGenerating,
    generatedImage,
    aspectRatio,
    setAspectRatio,
    errorMessage,
    handleGenerateArt: originalHandleGenerateArt,
  } = useArtGeneration(prompt, title, setTitle, artStyle);

  // Wrap the generate art function to reset save state
  const handleGenerateArt = () => {
    resetSaveState(); // Reset save state when generating new art
    originalHandleGenerateArt();
  };

  // Extract learning mode toggle
  const { learningMode, toggleLearningMode } = useLearningMode();

  // Extract portfolio save functionality
  const { isSaving, isSaved, portfolioImage, handleSaveToPortfolio, resetSaveState } =
    usePortfolioSave();

  // Create a handler for saving to portfolio that doesn't require parameters
  const handleSaveToPortfolioClick = () => {
    // Pass undefined for userId to let saveUserContent auto-detect the authenticated user
    handleSaveToPortfolio(
      undefined, // Let saveUserContent get the authenticated user automatically
      generatedImage,
      title,
      prompt,
      artStyle,
      aspectRatio
    );
  };

  console.log("ArtCreator: generatedImage =", generatedImage);

  return (
    <div className="container pt-12 pb-16">
      <ArtHeader
        learningMode={learningMode}
        toggleLearningMode={toggleLearningMode}
      />

      {/* Challenge Information */}
      {challengeData && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-bold text-orange-900 mb-2">
            🎨 Challenge: {challengeData.title}
          </h2>
          <p className="text-orange-800 mb-3">{challengeData.description}</p>

          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-orange-900 mb-2">Instructions:</h4>
              <p className="text-orange-700 whitespace-pre-line">{challengeData.instructions}</p>
            </div>

            <div>
              <h4 className="font-semibold text-orange-900 mb-2">Learning Goals:</h4>
              <ul className="text-orange-700 space-y-1">
                {challengeData.learningObjectives?.map((obj: {objective: string}, index: number) => (
                  <li key={index}>• {obj.objective}</li>
                ))}
              </ul>

              {challengeData.materials && challengeData.materials.length > 0 && (
                <div className="mt-3">
                  <h4 className="font-semibold text-orange-900 mb-2">Materials:</h4>
                  <ul className="text-orange-700 space-y-1">
                    {challengeData.materials.map((mat: {material: string}, index: number) => (
                      <li key={index}>• {mat.material}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="mt-4 flex items-center gap-4 text-sm text-orange-600">
            <span>⏱️ {challengeData.estimatedTime} minutes</span>
            <span>📊 {challengeData.difficulty}</span>
          </div>
        </div>
      )}

      <ArtCreatorContent
        prompt={prompt}
        setPrompt={setPrompt}
        title={title}
        setTitle={setTitle}
        handleGenerateArt={handleGenerateArt}
        handleGenerateIdea={handleGenerateIdea}
        handleImprovePrompt={handleImprovePrompt}
        isGenerating={isGenerating}
        isGeneratingIdea={isGeneratingIdea}
        isImprovingPrompt={isImprovingPrompt}
        generatedImage={generatedImage}
        artStyle={artStyle}
        setArtStyle={setArtStyle}
        aspectRatio={aspectRatio}
        setAspectRatio={setAspectRatio}
        errorMessage={errorMessage}
        isSaving={isSaving}
        isSaved={isSaved}
        portfolioImage={portfolioImage}
        handleSaveToPortfolio={handleSaveToPortfolioClick}
        learningMode={learningMode}
      />
    </div>
  );
};

export default ArtCreator;
